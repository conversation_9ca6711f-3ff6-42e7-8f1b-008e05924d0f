import {
  type NuiTheme,
  brandTheme,
  generateCompleteThemeCSS,
  neutralTheme,
} from "@next-ui/styles";
import type { Preset } from "unocss";
import { mapThemeToUnoCSS } from "./utils";

export interface PresetNextUIOptions {
  /** Array of themes to include in the preset */
  themes?: NuiTheme[];
  /** Whether to include CSS variables in the output */
  includeCSSVars?: boolean;
}

export const presetNextUI = (options: PresetNextUIOptions = {}): Preset => {
  const { themes = [neutralTheme, brandTheme], includeCSSVars = true } =
    options;

  // Generate CSS variables for all themes
  const themeCSS = includeCSSVars ? generateCompleteThemeCSS(themes) : "";

  return {
    name: "@next-ui/uno-preset",
    theme: mapThemeToUnoCSS(neutralTheme),
    preflights: includeCSSVars
      ? [
          {
            getCSS: () => themeCSS,
          },
        ]
      : undefined,
  };
};
