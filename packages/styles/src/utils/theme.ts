import { deepmerge } from "deepmerge-ts";

import { colorModes } from "../theme/colorModes";
import { colors } from "../theme/colors";
import { designTokens } from "../theme/designTokens";
import { variantTokens } from "../theme/variantTokens";
import type { NuiTheme } from "../types";

/**
 * Base theme with all default values
 */
const baseTheme: NuiTheme = {
  name: "Base",
  colors,
  modes: {
    light: colorModes.light,
    dark: colorModes.dark,
  },
  designTokens,
  variantTokens,
  components: {},
};

// Utility type for deep partials
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends Record<string, unknown>
    ? DeepPartial<T[P]>
    : T[P];
};

/**
 * Creates a theme by extending the base theme with custom overrides
 */
export const createTheme = (
  options:
    | (DeepPartial<NuiTheme> & { name: string })
    | ((base: NuiTheme) => DeepPartial<NuiTheme> & { name: string }),
): NuiTheme => {
  const resolved = typeof options === "function" ? options(baseTheme) : options;

  return deepmerge(baseTheme, resolved);
};
