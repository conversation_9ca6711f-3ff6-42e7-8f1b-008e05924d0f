import type { NuiTheme } from "../types";

/**
 * Flattens a nested object into CSS custom properties.
 */
const createCSSVarMap = (
  source: Record<string, unknown>,
  prefix = "",
): Record<string, string> => {
  const result: Record<string, string> = {};

  const flatten = (obj: Record<string, unknown>, path: string[] = []) => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = [...path, key];

      if (typeof value === "string") {
        const varName = `--${prefix ? `${prefix}-` : ""}${currentPath.join(
          "-",
        )}`;
        result[varName] = value;
      } else if (value && typeof value === "object" && !Array.isArray(value)) {
        flatten(value as Record<string, unknown>, currentPath);
      }
    }
  };

  flatten(source);
  return result;
};

/**
 * Generates the selector based on theme index and mode.
 */
const getSelector = (
  theme: NuiTheme,
  mode: string,
  isDefaultTheme: boolean,
  isDefaultMode: boolean,
): string => {
  if (isDefaultTheme && isDefaultMode) return ":root";
  if (isDefaultTheme) return `[data-theme-mode="${mode}"]`;
  if (isDefaultMode) return `[data-theme="${theme.name.toLowerCase()}"]`;
  return `[data-theme="${theme.name.toLowerCase()}"][data-theme-mode="${mode}"]`;
};

/**
 * Generates a complete CSS file with all themes, modes, and utility classes.
 */
export const generateCompleteThemeCSS = (themes: NuiTheme[]): string => {
  const cssBlocks: string[] = [];

  for (const [index, theme] of themes.entries()) {
    const isDefaultTheme = index === 0;

    for (const mode of Object.keys(theme.modes)) {
      const isDefaultMode = mode === "light";
      const selector = getSelector(theme, mode, isDefaultTheme, isDefaultMode);
      const modeTokens = theme.modes[mode];

      const cssVars = {
        ...createCSSVarMap(theme.colors, "colors"),
        ...createCSSVarMap(modeTokens as Record<string, unknown>, "tokens"),
        ...createCSSVarMap(theme.designTokens, "design"),
      };

      const declarations = Object.entries(cssVars)
        .map(([k, v]) => `  ${k}: ${v};`)
        .join("\n");

      cssBlocks.push(`${selector} {\n${declarations}\n}`);
    }
  }

  return [
    "/* Auto-generated theme CSS variables */",
    `/* Generated at: ${new Date().toISOString()} */`,
    "",
    ...cssBlocks,
    "",
    "/* Theme utility classes */",
    ".theme-transition {",
    "  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;",
    "}",
  ].join("\n");
};
