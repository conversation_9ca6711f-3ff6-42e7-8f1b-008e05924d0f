import type { Plugin } from "vite";
import type { NuiTheme } from "./types";
import { generateCompleteThemeCSS } from "./utils/css";

export interface NextUIVitePluginOptions {
  /** Array of themes to generate CSS for */
  themes: NuiTheme[];
}

/**
 * Simple Vite plugin for generating Next UI theme CSS variables at build time
 */
export function nextUIVitePlugin(options: NextUIVitePluginOptions): Plugin {
  const { themes } = options;
  let generatedCSS = "";

  return {
    name: "next-ui-theme-generator",
    buildStart() {
      console.log("🎨 Generating Next UI theme CSS variables...");
      try {
        generatedCSS = generateCompleteThemeCSS(themes);
        console.log("✅ Next UI theme CSS generation completed!");
      } catch (error) {
        console.error("❌ Error generating Next UI theme CSS:", error);
        throw error;
      }
    },

    generateBundle() {
      if (generatedCSS) {
        this.emitFile({
          type: "asset",
          fileName: "next-ui-themes.css",
          source: generatedCSS,
        });
      }
    },
  };
}
