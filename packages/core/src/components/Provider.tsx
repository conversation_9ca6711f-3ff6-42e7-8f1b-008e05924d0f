import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import type { NuiContextProps, NuiProviderProps } from "../types";
import { applyTheme, getMode, getTheme } from "../utils/theme";

export const NuiContext = createContext<NuiContextProps | undefined>(undefined);

export const NuiProvider = ({
  themes,
  initialTheme,
  initialMode,
  rootElement = "root",
  children,
}: NuiProviderProps) => {
  const [theme, setTheme] = useState(() => getTheme(themes, initialTheme));
  const [mode, setMode] = useState(() => getMode(theme, initialMode));

  useEffect(() => {
    applyTheme(theme, mode, rootElement);
  }, [theme, mode, rootElement]);

  const changeTheme = useCallback(
    (themeName: string) => {
      const newTheme = getTheme(themes, themeName);
      if (!newTheme || newTheme.name === theme.name) return;

      setTheme(newTheme);
      setMode(getMode(newTheme, initialMode));
    },
    [themes, theme.name, initialMode],
  );

  const changeMode = useCallback(
    (modeName: string) => {
      if (modeName !== mode && modeName in theme.modes) {
        setMode(modeName);
      }
    },
    [mode, theme.modes],
  );

  const contextValue = useMemo<NuiContextProps>(
    () => ({
      themes,
      theme,
      mode,
      changeTheme,
      changeMode,
    }),
    [themes, theme, mode, changeTheme, changeMode],
  );

  return (
    <NuiContext.Provider value={contextValue}>{children}</NuiContext.Provider>
  );
};
