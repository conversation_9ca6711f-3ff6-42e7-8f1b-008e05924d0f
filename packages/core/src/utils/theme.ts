import type { NuiTheme } from "@next-ui/styles";

/**
 * Returns a theme by name from a list of themes.
 * Falls back to the first theme if not found.
 */
export const getTheme = (themes: NuiTheme[], themeName?: string): NuiTheme => {
  return (
    themes.find(
      ({ name }) => name.toLowerCase() === themeName?.toLowerCase(),
    ) ?? themes[0]
  );
};

/**
 * Returns a valid mode name from a theme.
 * Defaults to the first available mode if not found or invalid.
 */
export const getMode = (theme: NuiTheme, modeName?: string): string => {
  const availableModes = Object.keys(theme.modes);
  return modeName && availableModes.includes(modeName)
    ? modeName
    : availableModes[0];
};

/**
 * Applies theme + mode attributes and class to the root DOM element.
 * Defaults to <html>, or an element with a given ID.
 */
export const applyTheme = (
  theme: NuiTheme,
  mode: string,
  rootElement = "root",
) => {
  const root =
    rootElement === "root"
      ? document.documentElement
      : document.getElementById(rootElement);

  if (!root) return;

  root.setAttribute("data-theme", theme.name.toLowerCase());
  root.setAttribute("data-theme-mode", mode);
  root.classList.add("theme-transition");
};
