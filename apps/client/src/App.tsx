import { useTheme } from "@next-ui/core";
import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="min-h-screen bg-background-primary px-6 py-10 text-sm text-text-primary">
      <main className="flex flex-col gap-12 max-w-md mx-auto">
        {/* Header */}
        <header className="text-center space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">Next UI</h1>
          <p className="text-text-secondary text-sm">
            {theme?.name} • {mode}
          </p>
        </header>

        {/* Theme Selector */}
        <section className="flex flex-col gap-2">
          <ThemeSelector />
        </section>

        {/* Colors */}
        <section className="flex flex-col gap-4">
          <h2 className="text-xs text-text-secondary font-medium uppercase tracking-wider">
            Colors
          </h2>
          <div className="grid grid-cols-4 gap-4">
            {[
              { color: "primary", label: "primary" },
              { color: "accent", label: "accent" },
              { color: "positive", label: "success" },
              { color: "negative", label: "error" },
            ].map(({ color, label }) => (
              <div key={color} className="space-y-2 text-center">
                <div className={`h-10 rounded-lg bg-${color}-base`} />
                <span className="text-text-secondary text-xs font-mono">
                  {label}
                </span>
              </div>
            ))}
          </div>
        </section>

        {/* Buttons */}
        <section className="flex flex-col gap-3">
          <h2 className="text-xs text-text-secondary font-medium uppercase tracking-wider">
            Buttons
          </h2>
          <button
            type="button"
            className="w-full bg-primary-base hover:bg-primary-hover text-white py-3 px-4 rounded-lg transition-all font-medium shadow-sm hover:shadow-md"
          >
            Primary Action
          </button>
          <button
            type="button"
            className="w-full bg-background-secondary hover:bg-background-surface border border-border-default text-text-secondary py-3 px-4 rounded-lg transition-all shadow-sm hover:shadow-md"
          >
            Secondary Action
          </button>
        </section>

        {/* Alerts */}
        <section className="flex flex-col gap-3">
          <h2 className="text-xs text-text-secondary font-medium uppercase tracking-wider">
            Alerts
          </h2>
          <div className="rounded-lg border-l-4 border-positive-base bg-positive-subtle text-positive-base p-4 shadow-sm">
            <div className="font-medium">Success</div>
            <div className="opacity-75 mt-1 text-xs font-mono">
              bg-positive-subtle
            </div>
          </div>
          <div className="rounded-lg border-l-4 border-negative-base bg-negative-subtle text-negative-base p-4 shadow-sm">
            <div className="font-medium">Error</div>
            <div className="opacity-75 mt-1 text-xs font-mono">
              bg-negative-subtle
            </div>
          </div>
        </section>

        {/* Tokens */}
        <section className="flex flex-col gap-2 text-xs font-mono text-center">
          <h2 className="text-xs text-text-secondary font-medium uppercase tracking-wider">
            Tokens
          </h2>
          <div className="text-text-secondary">text-text-secondary</div>
          <div className="text-primary-base">text-primary-base</div>
          <div className="border border-border-default p-2 rounded text-text-secondary">
            border-border-default
          </div>
        </section>
      </main>
    </div>
  );
};
