import { brandTheme, neutralTheme } from "@next-ui/styles";
import { presetNextUI } from "@next-ui/uno-preset";
import { defineConfig, presetWind3, transformerVariantGroup } from "unocss";

export default defineConfig({
  presets: [
    presetWind3(),
    presetNextUI({
      themes: [neutralTheme, brandTheme],
      includeCSSVars: true,
    }),
  ],
  transformers: [transformerVariantGroup()],
  content: {
    pipeline: {
      include: [/\.jsx?$/, /\.tsx?$/, /\.mdx?$/, /\.html$/],
    },
  },
});
