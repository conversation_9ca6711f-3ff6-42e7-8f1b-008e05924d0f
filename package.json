{"name": "@next-ui/next-ui", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "bun --filter './apps/*' dev", "build": "bun run build:css && bun --filter './apps/*' build", "build:css": "bun --filter '@next-ui/styles' build:css", "check": "biome check --write", "typecheck": "tsc --noEmit", "depcheck": "npx skott", "clean": "find . -name node_modules -o -name bun.lock | xargs rm -rf"}, "devDependencies": {"@biomejs/biome": "1.9.4", "typescript": "^5.8.2"}}